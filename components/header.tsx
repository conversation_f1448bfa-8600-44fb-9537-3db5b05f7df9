import { But<PERSON> } from "@/components/ui/button"
import { Scale } from "lucide-react"
import Link from "next/link"

export function Header() {
  return (
    <header className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <Scale className="h-8 w-8" />
            <span className="text-xl font-bold">Find Car Accident Attorneys</span>
          </Link>

          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/" className="hover:text-accent transition-colors">
              Home
            </Link>
            <Link href="/search" className="hover:text-accent transition-colors">
              Lawyers
            </Link>
            <Link href="/about" className="hover:text-accent transition-colors">
              About
            </Link>
            <Link href="/contact" className="hover:text-accent transition-colors">
              Contact
            </Link>
          </nav>

          <div className="flex items-center space-x-2">
            <Button variant="secondary" size="sm">
              Sign In
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary bg-transparent"
            >
              Register
            </Button>
          </div>
        </div>
      </div>
    </header>
  )
}
